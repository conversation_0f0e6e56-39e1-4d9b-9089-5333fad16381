"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Modal from "react-modal";
import { AlertCircle, Check } from "lucide-react";
import { Dot } from "lucide-react";
import { signOut, fetchAuthSession } from "aws-amplify/auth";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

import {
  apiCall,
  getLookUpData,
  getStudentApplications,
  getOapForm,
  getOapFormSections,
  saveOapForm,
  getStudentDetails,
  getAccessToken,
  getOapDetail,
} from "@/api/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import { useRouter } from "next/navigation";
import loader from "../../public/loader.svg";
import loader2 from "../../public/loader2.svg";
import ReactMarkdown from "react-markdown";
import { useAtom } from "jotai";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import {
  applicationId,
  consumerAPIKey,
  email,
  nextForm,
  preferredLanguage,
  qualifyingQuestions,
  staticContentsAtom,
} from "@/lib/atom";
import { checkVisibility } from "@/lib/visibilityUtils";
import { LayoutWrapper } from "../custom/wrapper";
import { LinkRenderer } from "../custom/linkRender";
import { sortOrder } from "@/helpers/Sorting";
import { useProgress } from "@/lib/progress-context";
import { QuestionnairePopup } from "../custom/questionnaire-popup";
import LanguageSelector from "../custom/translate";
import Link from "next/link";

const ApplicationFilter = () => {
  const [applicationFields, setApplicationFields] = useState<any>({});
  const [dropdownOptions, setDropdownOptions] = useState<any>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [apiKey] = useAtom(consumerAPIKey);
  const [modalIsOpen, setIsOpen] = useState<boolean>(false);
  const [programDetails, setProgramDetails] = useState<any>({});
  const [popoverVisible, setPopoverVisible] = useState<any>(null);
  const [isFetchingPdfUrl, setIsFetchingPdfUrl] = useState<any>(false);
  const [isLoggingOut, setIsLoggingOut] = useState<any>(null);
  const [applicationDetails, setApplicationDetails] = useState<any>(null);
  const { setProgress, progress } = useProgress();
  const [preferLang, setPreferLang] = useAtom(preferredLanguage);
  const [showQuestionarrieModal, setShowQuestionarrieModal] =
    useState<boolean>(false);
  const [staticContents] = useAtom(staticContentsAtom);

  const handleMouseEnter = (application: any) => {
    setPopoverVisible(application);
  };

  const handleMouseLeave = () => {
    setPopoverVisible(null);
  };

  const queryClient = useQueryClient();
  const router = useRouter();

  const [nextFormDetails, setNextFormDetails]: any = useAtom(nextForm);
  const [userEmail, setUserEmail] = useAtom(email);
  const [application, setApplication] = useAtom(applicationId);
  const [qualifyingQuestionsState] = useAtom<any>(qualifyingQuestions);
  const [errors, setErrors]: any = useState({});
  const { register, reset, setValue, watch } = useFormContext();
  const [fontSize] = useAtom(fontSizeAtom);

  const { data: labels } = useQuery({
    queryKey: [`oap/lookup/programmes`],
    queryFn: async () => {
      let res = await getLookUpData(
        {
          name: `oap/lookup/programmes?brand=${process.env.NEXT_PUBLIC_OAP_NAME}&email=${userEmail}&byProduct=true`,
        },
        apiKey,
        await getAccessToken()
      );
      return res;
    },
    enabled: !!apiKey && process.env.NEXT_PUBLIC_OAP_NAME === "UCW",
  });

  useEffect(() => {
    const fetchUserEmail = async () => {
      const basicDetails = JSON.parse(
        localStorage.getItem("basic-details") ?? "[]"
      );
      if (basicDetails?.email) {
        setUserEmail(basicDetails?.email);
      } else {
        const userDetails = await fetchAuthSession();
        const emailValue = userDetails?.tokens?.idToken?.payload?.email;
        setUserEmail(typeof emailValue === "string" ? emailValue : "");
        localStorage.setItem(
          "basic-details",
          JSON.stringify({
            email: emailValue,
            firstName:
              userDetails?.tokens?.idToken?.payload?.["custom:first_name"],
            lastName:
              userDetails?.tokens?.idToken?.payload?.["custom:last_name"],
          })
        );
      }
    };
    fetchUserEmail();
  }, []);

  const {
    data: myApplications,
    refetch: refetchApplications,
    isFetching: myApplicationsIsFetching,
  } = useQuery({
    queryKey: [`myApplications`, preferLang],
    queryFn: async () => {
      let res = await getStudentApplications(
        {
          email: userEmail,
          brand: process.env.NEXT_PUBLIC_OAP_NAME,
          ...(preferLang !== "en" && { language: preferLang }),
        },
        apiKey,
        await getAccessToken()
      );
      return res;
    },
    enabled: !!apiKey && !!userEmail,
  });

  const {
    data: sectionQuery,
    isFetching: sectionIsFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [
      `${nextFormDetails?.oap}-${nextFormDetails?.mode}-${nextFormDetails?.form}`,
    ],
    queryFn: async () => {
      if (
        nextFormDetails?.form &&
        nextFormDetails?.oap &&
        nextFormDetails?.mode
      ) {
        let res = await getOapForm(
          {
            oap: nextFormDetails?.oap,
            form: nextFormDetails?.form,
            mode: nextFormDetails?.mode,
            ...(preferLang === "de" && { language: "de" }),
          },
          apiKey,
          await getAccessToken()
        );
        return res;
      } else {
        return;
      }
    },
    enabled: !!nextFormDetails,
  });

  const handlePreQualificationResubmit = (
    e: React.MouseEvent<HTMLAnchorElement, MouseEvent>
  ) => {
    e.preventDefault();
    router.push("/app-prequalifying-questions");
    return;
  };

  const {
    data: formQuery,
    isFetching: formQueryIsFetching,
    refetch: refetchFormQuery,
  } = useQuery({
    // next sections page
    queryKey: [`test`],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.name && sectionQuery?.form)) return;
      let res = await getOapFormSections(
        {
          oap: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          formName: sectionQuery?.form,
          sectionName: sectionQuery?.section?.[0]?.name,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey,
        await getAccessToken()
      );
      return res;
    },
    enabled: !!(nextFormDetails && sectionQuery),
  });

  const object = formQuery?.fieldData?.[0]?.popupDetails?.fieldData
    ? formQuery?.fieldData?.[0]?.popupDetails?.fieldData
    : formQuery?.fieldData;

  const { data: questionariesSectionQuery } = useQuery({
    queryKey: [`questionnaire-section`, application],
    queryFn: async () => {
      let res = await getOapFormSections(
        {
          oap: process.env.NEXT_PUBLIC_OAP_NAME,
          mode: process.env.NEXT_PUBLIC_OAP_MODE,
          formName: "PROGRAM_FILTER",
          sectionName: "QUESTIONNAIRE_POPUP",
        },
        apiKey
      );
      return res;
    },
    enabled:
      !!apiKey &&
      formQuery?.fieldData?.filter((item: any) => item.type == "button")[0]
        ?.canHaveQuestionnarieFields,
  });

  // Load database configuration to access requestTypes
  const { data: pageQuery } = useQuery({
    queryKey: [
      `${process.env.NEXT_PUBLIC_OAP_NAME}-${process.env.NEXT_PUBLIC_OAP_MODE}`,
    ],
    queryFn: async () => {
      return await getOapDetail(
        {
          name: process.env.NEXT_PUBLIC_OAP_NAME,
          mode: process.env.NEXT_PUBLIC_OAP_MODE,
        },
        apiKey
      );
    },
    enabled: !!apiKey,
  });

  useEffect(() => {
    reset();
  }, []);

  useQuery({
    queryKey: [`next-form-screen`],
    queryFn: async () => {
      let tempObj = {
        email: nextFormDetails.email,
        applicationId: application,
      };

      object?.forEach((ele: any) => {
        if (ele.action == "nextForm") {
          tempObj = { ...tempObj, ...ele[ele.action] };
        }
      });
      return tempObj;
    },
    enabled: object?.length > 0,
  });

  function flattenPayload(payload: any) {
    const flattened: any = {};
    for (const key in payload) {
      if (Object.prototype.hasOwnProperty.call(payload, key)) {
        if (typeof payload[key] === "object" && payload[key] !== null) {
          flattened[key] = payload[key].value;
        } else {
          flattened[key] = payload[key];
        }
      }
    }
    return flattened;
  }

  const getNextForm = () => {
    const formDetail = object?.find(
      (item: any) => item?.type === "button"
    )?.nextForm;
    return formDetail;
  };

  function validateOutput(outputObject: any, fieldData: any) {
    let temp: any[] = [];
    if (!fieldData) {
      return temp;
    }
    fieldData.forEach((field: any) => {
      const { fieldName, required, rules, visibleWhen } = field;
      if (checkFieldVisibility(visibleWhen)) {
        if (Object.keys(outputObject).length == 0) {
          temp.push(rules?.required);

          if (rules) {
            setErrors((prev: any) => ({
              ...prev,
              [fieldName]: rules?.required,
            }));
          }
        } else {
          const fieldValue = outputObject[fieldName];
          if (!fieldName) {
          } else if (required && !(fieldName in outputObject)) {
            temp.push(rules?.required);
            setErrors((prev: any) => ({
              ...prev,
              [fieldName]: rules?.required,
            }));
          } else if (fieldValue == undefined || fieldValue == null) {
            temp.push(rules?.required);
            setErrors((prev: any) => ({
              ...prev,
              [fieldName]: rules?.required,
            }));
          }
        }
      }
    });
    return temp;
  }
  const handleCancelationApplication = (questionnaireFields?: any) => {
    (async () => {
      let temp = flattenPayload(applicationFields);

      const basicDetails = JSON.parse(
        localStorage.getItem("basic-details") ?? "[]"
      );
      let isValid = validateOutput(temp, object);

      if (isValid.length !== 0) {
        setSaving((prev: boolean) => !prev);
        return;
      }

      let res: any = {};

      let basePayload = {
        email: userEmail,
        applicationStatus: "inProgress",
        ...temp,
        ...basicDetails,
        sectionLabel: formQuery?.label,
      };

      if (Object.keys(programDetails).length > 0) {
        basePayload = {
          ...basePayload,
          ...programDetails,
        };
      }
      if (Object.keys(questionnaireFields || {}).length > 0) {
        basePayload = {
          ...basePayload,
          ...questionnaireFields,
        };
      }
      if (Object.keys(applicationDetails || {}).length > 0) {
        basePayload = {
          ...basePayload,
          ...applicationDetails,
        };
        setApplicationDetails({});
      }

      res = await saveOapForm(
        basePayload,
        { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
        apiKey,
        await getAccessToken()
      );
    })();
  };

  const handleSaveApplication = (questionnaireFields?: any) => {
    setSaving((prev: boolean) => !prev);

    (async () => {
      let temp = flattenPayload(applicationFields);

      const basicDetails = JSON.parse(
        localStorage.getItem("basic-details") ?? "[]"
      );

      let isValid = validateOutput(temp, object);

      if (isValid.length !== 0) {
        setSaving(() => false);
        return;
      }

      if (formQuery?.canHavePathwayPartners && !modalIsOpen) {
        setIsOpen(true);
        setSaving((prev: boolean) => false);
        return;
      }

      let res: any = {};

      // Add UTM parameters to registration data only if they exist
      const utmParams = sessionStorage.getItem("utmParams");
      if (utmParams) {
        const parsedUtmParams = utmParams ? JSON.parse(utmParams) : {};
        if (Object.keys(parsedUtmParams).length > 0) {
          temp.utmParams = parsedUtmParams;
        }
      }

      let basePayload = {
        email: userEmail,
        applicationStatus: "inProgress",
        ...temp,
        ...basicDetails,
        ...questionnaireFields,
        sectionLabel: formQuery?.label,
      };

      if (Object.keys(programDetails).length > 0) {
        basePayload = {
          ...basePayload,
          ...programDetails,
        };
      }
      if (Object.keys(questionnaireFields || {}).length > 0) {
        basePayload = {
          ...basePayload,
          ...questionnaireFields,
        };
      }

      if (Object.keys(applicationDetails || {}).length > 0) {
        basePayload = {
          ...basePayload,
          ...applicationDetails,
        };
        setApplicationDetails({});
      }

      // for qualifying questions.
      const hasQualifyingQuestions = qualifyingQuestionsState;

      if (hasQualifyingQuestions) {
        basePayload = {
          ...basePayload,
          ...qualifyingQuestionsState,
        };
      }

      res = await saveOapForm(
        basePayload,
        { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
        apiKey,
        await getAccessToken()
      );

      await queryClient.setQueryData(["next-form-screen"], () => {
        let tempObj = {};
        object?.forEach((ele: any) => {
          if (ele.action == "nextForm") {
            tempObj = {
              email: res?.email,
              applicationId: res?.applicationId,
              ...ele[ele.action],
            };
          }
        });
        return tempObj;
      });

      if (res?.email && res?.applicationId) {
        let nextForm = await getNextForm();
        let nextApply = basicDetails?.[nextForm?.dependentField];

        setNextFormDetails(getNextForm());
        setUserEmail(res?.email);
        setApplication(res?.applicationId);
        setProgress(res?.progress);
        Object.keys(res || {}).forEach((key) => {
          if (key.startsWith("ocrReprocessCount_")) {
            const savedValue = res[key] ?? 0;
            setValue(key, savedValue);
          }
        });

        // Force a small delay to ensure setValue takes effect after any reset
        setTimeout(() => {
          Object.keys(res || {}).forEach((key) => {
            if (key.startsWith("ocrReprocessCount_")) {
              const savedValue = res[key] ?? 0;
              setValue(key, savedValue);
            }
          });
        }, 100);

        if (nextForm?.type == "multiple") {
          router.push(`/form?apply=${nextApply}&step=0`);
          return;
        }

        if (nextFormDetails?.type == "single") {
          router.push(`/form?apply=${getNextForm()?.form}&step=0`);
          return;
        }
        router.push(
          `/form?apply=${applicationFields?.applicationType?.label}&step=0`
        );
        return;
      }
    })();
  };

  const findIsDuplicateApplication: any = (programId: any) => {
    const duplicateApplications = myApplications.filter(
      (applicationDetail: any) => applicationDetail.programId === programId
    );

    return duplicateApplications?.length === 0;
  };

  const handleShowModal = (
    label: any,
    value: any,
    level: any,
    levelId: any
  ) => {
    (async () => {
      const fieldDisplayName = formQuery?.fieldData?.[0]?.fieldDisplayName;
      const fieldName = formQuery?.fieldData?.[0]?.fieldName;

      const response = {
        [fieldDisplayName]: label,
        [fieldName]: value,
        levelDisplayName: level,
        level: levelId,
      };

      setApplicationDetails(response);
    })();
  };

  useEffect(() => {
    (async () => {
      let item: any = {};
      if (formQuery?.canHavePathwayPartners) {
        item = getPopupDetails(formQuery).find(
          (ele: any) => ele.type === "pickList"
        );
      }

      let url = "oap/lookup";
      if (item?.picklistSourceType == "Inline") {
        let picklistValues = item.pickListValues;

        picklistValues = myApplications
          ? picklistValues?.filter(
              (data: any) =>
                !myApplications.some((app: any) => app.programId === data.value)
            )
          : picklistValues;

        setDropdownOptions((prev: any) => ({
          ...prev,
          [item?.fieldName]: picklistValues,
        }));
      } else {
        if (item?.picklistSource == undefined) return;
        if (item?.picklistSource?.includes("${")) {
          let originalString = item?.picklistSource;
          const regex = /\${(.*?)}/g;
          const replacedString = originalString.replace(
            regex,
            (match: string, group: string) => {
              const applicationDetails = JSON.parse(
                localStorage.getItem("basic-details") || "{}"
              );
              if (!isNaN(applicationFields[group]?.value)) {
                // checking number values
                return Number(applicationFields[group]?.value);
              }
              return (
                applicationFields[group]?.value ||
                applicationFields[group] ||
                (applicationDetails && typeof applicationDetails === "object"
                  ? applicationDetails[group]?.value ||
                    applicationDetails[group]
                  : null) ||
                match
              );
            }
          );
          setIsLoading(true);
          let data = await apiCall(
            `${replacedString}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );

          setIsLoading(false);
          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: data,
          }));
        } else {
          let data = await apiCall(
            `${item?.picklistSource}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );

          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: data,
          }));
        }
      }
    })();
  }, [formQuery]);

  useEffect(() => {
    (async () => {
      const item =
        Object.keys(applicationFields).length != 0
          ? applicationFields.item
          : formQuery?.fieldData[0];
      let url = "oap/lookup";
      if (item?.picklistSourceType == "Inline") {
        let picklistValues = item.pickListValues;

        picklistValues = myApplications
          ? picklistValues?.filter(
              (data: any) =>
                !myApplications.some((app: any) => app.programId === data.value)
            )
          : picklistValues;

        setDropdownOptions((prev: any) => ({
          ...prev,
          [item?.fieldName]: picklistValues,
        }));
      } else {
        if (item?.picklistSource == undefined) return;
        if (item?.picklistSource?.includes("${")) {
          let originalString = item?.picklistSource;
          const regex = /\${(.*?)}/g;
          const replacedString = originalString.replace(
            regex,
            (match: string, group: string) => {
              const applicationDetails: any = JSON.parse(
                localStorage.getItem("basic-details") || "{}"
              );
              if (!isNaN(applicationFields[group]?.value)) {
                // checking number values
                return Number(applicationFields[group]?.value);
              }
              return (
                applicationFields[group]?.value ||
                applicationFields[group] ||
                (applicationDetails && typeof applicationDetails === "object"
                  ? applicationDetails[group]?.value ||
                    applicationDetails[group]
                  : null) ||
                match
              );
            }
          );
          setIsLoading(true);
          let data = await apiCall(
            `${replacedString}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );
          setIsLoading(false);
          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: data,
          }));
        } else {
          let data = await apiCall(
            `${item?.picklistSource}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );

          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: data,
          }));
        }
      }
    })();
  }, [applicationFields.level, formQuery, applicationFields, myApplications]);

  // Use centralized visibility logic with state fallback
  const checkFieldVisibility = (visibleWhenProps: any) => {
    return checkVisibility(visibleWhenProps, watch);
  };
  function closeModal() {
    setIsOpen(false);
    // setApplicationFields({});
    // refetchApplications();
  }
  function handleContinueApplication(applicationDetails: any) {
    Object.keys(applicationDetails || {}).forEach((key) => {
      if (key.startsWith("ocrReprocessCount_")) {
        const value = applicationDetails[key] ?? 0;
        setValue(key, value);
      }
    });

    setUserEmail(userEmail);
    setApplication(applicationDetails.applicationId);
    setProgress(applicationDetails.progress);
    setNextFormDetails(getNextForm());
    router.push(`/form?apply=${getNextForm()?.form}&step=0`);
  }

  async function handleLogout() {
    try {
      setIsLoggingOut(true);
      await signOut();
      localStorage.clear();
      sessionStorage.removeItem("utmParams");
      router.push("/login");
      setIsLoggingOut(false);
    } catch (err) {
      console.error(err);
    }
  }

  // Add this helper function to get popup details
  const getPopupDetails = (formQuery: any) => {
    // Case 1: Popup details at root level
    if (formQuery?.popupDetails?.fieldData) {
      return formQuery?.popupDetails?.fieldData;
    }

    // Case 2: Popup details inside fieldData[0]
    if (formQuery?.fieldData?.[0]?.popupDetails?.fieldData) {
      return formQuery?.fieldData?.[0]?.popupDetails?.fieldData;
    }

    // Case 3: Popup details inside programCards field
    const programCardsField = formQuery?.fieldData?.find(
      (field: any) => field.type === "programCards"
    );
    if (programCardsField?.popupDetails?.fieldData) {
      return programCardsField?.popupDetails?.fieldData;
    }

    return null;
  };

  useEffect(() => {
    refetchSectionQuery();
    refetchFormQuery();
  }, [preferLang]);

  if (
    sectionIsFetching ||
    formQueryIsFetching ||
    saving ||
    myApplicationsIsFetching
  ) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll w-full">
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }

  return (
    <>
      {formQuery && (
        <div className="w-full">
          <div className=" w-full  ">
            <header className="p-6 header bg-on-background flex gap-4 w-full max-h-[98px] justify-between ">
              <div className="header-brand ">
                <a className="header-brand-link">
                  {sectionQuery && sectionQuery?.logoInfo?.signedUrl && (
                    <Image
                      className="header -brand-image"
                      src={sectionQuery.logoInfo.signedUrl}
                      alt="ucw_logo_int"
                      width={sectionQuery.logoInfo.width}
                      height={sectionQuery.logoInfo.height}
                    />
                  )}
                </a>
              </div>
              <div className="header-content text-highlight text-xl font-normal items-center justify-between  flex-1  sm:flex hidden ">
                <div className="header-title header-title_general border-l border-[#] pl-[34px] flex items-center justify-between w-full">
                  <div className="header-title-value">
                    {sectionQuery?.headerTitle}
                  </div>
                  {sectionQuery?.languageData &&
                    sectionQuery?.languageData.canShowLanguageSwitch && (
                      <div className="mr-4">
                        <LanguageSelector
                          languageData={sectionQuery?.languageData}
                        />
                      </div>
                    )}
                </div>

                <div className="flex items-center gap-4 ml-auto">
                  <button
                    className="button button_primary bg-secondary text-sm font-bold text-white rounded cursor-pointer w-full  flex justify-center items-center whitespace-nowrap px-4 py-2"
                    onClick={() => handleLogout()}
                  >
                    {isLoggingOut ? (
                      <Image
                        priority
                        src={loader}
                        height={32}
                        width={32}
                        style={{ objectFit: "contain" }}
                        alt="loader"
                      />
                    ) : (
                      <p>{staticContents?.application?.logout || "Logout"}</p>
                    )}
                  </button>
                </div>
              </div>
              <div className="header-content header-content_mini">
                <div className="header-profile">
                  <div
                    className="header-profile-user-languages"
                    id="userLanguages"
                  ></div>
                </div>
              </div>
              <div className="visible sm:hidden lg:hidden md:hidden xl:hidden 2xl:hidden">
                <button
                  className="button text-sm font-bold text-white rounded cursor-pointer w-full  flex  items-center whitespace-nowrap px-4 py-2 "
                  onClick={() => handleLogout()}
                >
                  <svg
                    width="24px"
                    height="24px"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                  >
                    <path
                      stroke="#ffff"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M20 12h-9.5m7.5 3l3-3-3-3m-5-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2h5a2 2 0 002-2v-1"
                    />
                  </svg>
                </button>
              </div>
            </header>
            <div className=" lg:px-12 h-full lg-mb-12">
              <LayoutWrapper>
                <div className="prose dark:prose-invert mb-4 max-w-none">
                  <ReactMarkdown
                    className="markDown m-4"
                    components={{ a: LinkRenderer }}
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                  >
                    {sectionQuery?.description}
                  </ReactMarkdown>
                </div>
                {myApplications?.length ? (
                  <div className="index-widget shadow-[0_3px_10px_rgb(0,0,0,0.2)] rounded p-5 sm:p-14 md:p-14 lg:14 xl:14 lg:mb-[50px] sm:mb-0 md:mb-0  bg-primary">
                    <div className="index-widget-wrapper">
                      <form className="form" noValidate>
                        <div
                          className={`form-section w-full flex flex-col  gap-4`}
                        >
                          <h3
                            className="form-section-title text-white font-semibold pb-8  text-xl"
                            style={getBrandSpecificFontStyle(
                              fontSize,
                              "card-title"
                            )}
                          >
                            {staticContents?.application?.myApplications ||
                              "My Applications"}
                            ({myApplications?.length})
                          </h3>
                        </div>
                        {myApplications?.length > 1 && (
                          <div className="w-full flex flex-wrap  gap-4 ">
                            {myApplications?.map(
                              (applicationDetails: any, index: any) => {
                                // Check if change request functionality should be available
                                const showChangeRequestButton =
                                  applicationDetails?.isCompleted &&
                                  pageQuery?.requestTypes &&
                                  Array.isArray(pageQuery.requestTypes) &&
                                  pageQuery.requestTypes.length > 0;

                                return (
                                  <div
                                    key={index}
                                    style={{
                                      backgroundColor: "#f2f2f2",
                                      backgroundImage: `url(${formQuery?.layout?.backgroundImage})`,
                                      backgroundSize: "auto",
                                      backgroundRepeat: "no-repeat",
                                      backgroundPosition: "right center",
                                    }}
                                    className="border border-gray-30 w-full p-4 rounded-lg flex flex-col justify-between  lg:min-h-48 lg:w-[48%] xs:w-[90%] mb-4"
                                  >
                                    <div className="flex justify-between gap-5 items-center mb-4">
                                      <p
                                        className="text-lg font-bold"
                                        style={getBrandSpecificFontStyle(
                                          fontSize,
                                          "header-title"
                                        )}
                                      >
                                        {applicationDetails?.programName}
                                      </p>
                                      <div className="relative inline-block">
                                        <div
                                          className="button button_primary bg-[#212a33] hover:bg-secondary text-white font-bold rounded cursor-pointer w-full  flex justify-center items-center whitespace-nowrap px-3 py-2"
                                          onMouseEnter={() =>
                                            handleMouseEnter(applicationDetails)
                                          }
                                          onMouseLeave={handleMouseLeave}
                                        >
                                          {applicationDetails?.isCompleted && (
                                            <p className="text-xs font-semibold">
                                              100%{" "}
                                              {staticContents?.application
                                                ?.completed || "Completed"}
                                            </p>
                                          )}
                                          {!applicationDetails?.isCompleted && (
                                            <p className="text-xs font-semibold">
                                              {
                                                applicationDetails?.completionPercentage
                                              }
                                              %{" "}
                                              {staticContents?.application
                                                ?.completed || "Completed"}
                                            </p>
                                          )}
                                        </div>
                                        {popoverVisible?.applicationId ===
                                          applicationDetails?.applicationId &&
                                          applicationDetails?.sections
                                            ?.length && (
                                            <div className="absolute lg:w-[500px] md:w-[500px] xl:w-[500px] w-[250px]  z-[1000] bg-white border border-gray-300 rounded shadow-lg p-4 gap-4 flex flex-wrap  top-full mt-2 -right-10">
                                              <p className="font-bold text-base">
                                                {staticContents?.application
                                                  ?.completeSteps ||
                                                  "Complete application with following steps"}
                                              </p>

                                              <div className="grid lg:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 grid-cols-1 gap-2 gap-x-12">
                                                {sortOrder(
                                                  applicationDetails?.sections,
                                                  "displayOrder"
                                                ).map(
                                                  (
                                                    section: any,
                                                    index: any
                                                  ) => (
                                                    <div
                                                      key={index}
                                                      className="flex gap-4"
                                                    >
                                                      <span
                                                        className={
                                                          "text-sm text-[#6a7580]"
                                                        }
                                                      >
                                                        {section.status ||
                                                        applicationDetails.isCompleted ? (
                                                          <Check
                                                            name="Check"
                                                            height={15}
                                                            width={15}
                                                            className="font-bold text-primary"
                                                          />
                                                        ) : (
                                                          <Dot
                                                            name="Check"
                                                            height={15}
                                                            width={15}
                                                            className="font-bold"
                                                          />
                                                        )}
                                                      </span>
                                                      <p className="text-sm text-[#6a7580]">
                                                        {section.name}
                                                      </p>
                                                    </div>
                                                  )
                                                )}
                                              </div>
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                    <div className="flex flex-col xl:flex-row items-center gap-2">
                                      {applicationDetails?.isCompleted && (
                                        <button
                                          className="button button_primary bg-primary hover:bg-secondary text-white font-bold h-10 rounded cursor-pointer w-full lg:w-[200px] p-2 flex justify-center items-center"
                                          style={getBrandSpecificFontStyle(
                                            fontSize,
                                            "label"
                                          )}
                                          onClick={async (e) => {
                                            e.preventDefault();
                                            setIsFetchingPdfUrl(
                                              applicationDetails.applicationId
                                            );

                                            const response =
                                              await getStudentDetails(
                                                nextFormDetails?.oap,
                                                userEmail,
                                                applicationDetails?.applicationId,
                                                apiKey,
                                                await getAccessToken()
                                              );

                                            setIsFetchingPdfUrl("");

                                            if (response?.signedUrl) {
                                              window.open(
                                                response.signedUrl,
                                                "_blank"
                                              );
                                            }
                                          }}
                                        >
                                          {isFetchingPdfUrl ===
                                          applicationDetails.applicationId ? (
                                            <Image
                                              priority
                                              src={loader}
                                              height={32}
                                              width={32}
                                              style={{ objectFit: "contain" }}
                                              alt="loader"
                                            />
                                          ) : (
                                            staticContents?.application
                                              ?.viewApplicationForm ||
                                            "View Application Form"
                                          )}
                                        </button>
                                      )}
                                      {showChangeRequestButton && (
                                        <Link
                                          href={`/change-request-form?applicationFormId=${applicationDetails.applicationId}`}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="button button_primary bg-secondary hover:bg-primary text-white font-bold h-10 rounded cursor-pointer w-full lg:w-[200px] p-2 flex justify-center items-center"
                                          style={getBrandSpecificFontStyle(
                                            fontSize,
                                            "label"
                                          )}
                                        >
                                          Change Request
                                        </Link>
                                      )}
                                    </div>
                                    {!applicationDetails?.isCompleted && (
                                      <button
                                        className="button button_primary bg-primary hover:bg-secondary text-white font-bold h-10 rounded cursor-pointer w-full lg:w-[200px] p-2 flex justify-center items-center"
                                        style={getBrandSpecificFontStyle(
                                          fontSize,
                                          "label"
                                        )}
                                        onClick={(e) => {
                                          e.preventDefault();

                                          handleContinueApplication(
                                            applicationDetails
                                          );
                                        }}
                                      >
                                        {staticContents?.application
                                          ?.continueApplication ||
                                          "Continue Application"}
                                      </button>
                                    )}
                                  </div>
                                );
                              }
                            )}
                          </div>
                        )}
                        {myApplications?.length === 1 && (
                          <div className="">
                            {myApplications?.map(
                              (applicationDetails: any, index: any) => {
                                // Check if change request functionality should be available
                                const showChangeRequestButton =
                                  applicationDetails?.isCompleted &&
                                  pageQuery?.requestTypes &&
                                  Array.isArray(pageQuery.requestTypes) &&
                                  pageQuery.requestTypes.length > 0;

                                return (
                                  <div
                                    key={index}
                                    style={{
                                      backgroundColor: "#f2f2f2",
                                      backgroundImage: `url(${formQuery?.layout?.backgroundImage})`,
                                      backgroundSize: "auto",
                                      backgroundRepeat: "no-repeat",
                                      backgroundPosition: "right center",
                                    }}
                                    className="border  border-gray-300 p-4 rounded-lg flex flex-col  xs:w-[90%] mb-4"
                                  >
                                    <div className="flex justify-between gap-2  mb-4">
                                      <p className="text-lg font-bold">
                                        {applicationDetails?.programName}
                                      </p>
                                      <div className="form-action hidden   w-full flex-col xl:flex sm:flex-row lg:flex-row md:flex-row lg:pl-8 text-sm sm:gap-10">
                                        {applicationDetails?.sections?.length >
                                          0 && (
                                          <>
                                            <div className="flex-shrink-0 flex flex-col ">
                                              {sortOrder(
                                                applicationDetails?.sections,
                                                "displayOrder"
                                              )
                                                .slice(
                                                  0,
                                                  Math.ceil(
                                                    applicationDetails?.sections
                                                      ?.length / 2
                                                  )
                                                )
                                                .map((item: any, i: number) => (
                                                  <div
                                                    key={i}
                                                    className="flex items-center gap-x-2"
                                                  >
                                                    {item?.status ||
                                                    applicationDetails?.isCompleted ? (
                                                      <Check
                                                        name="Check"
                                                        height={15}
                                                        width={15}
                                                        className="font-bold text-primary"
                                                      />
                                                    ) : (
                                                      <Dot
                                                        name="Check"
                                                        height={15}
                                                        width={15}
                                                        className="font-bold"
                                                      />
                                                    )}
                                                    <p>{item?.name}</p>
                                                  </div>
                                                ))}
                                            </div>

                                            <div className="w-full  flex flex-col flex-nowrap">
                                              {sortOrder(
                                                applicationDetails?.sections,
                                                "displayOrder"
                                              )
                                                .slice(
                                                  Math.ceil(
                                                    applicationDetails.sections
                                                      .length / 2
                                                  )
                                                )
                                                .map((item: any, i: number) => (
                                                  <div
                                                    key={
                                                      i +
                                                      Math.ceil(
                                                        applicationDetails
                                                          .sections.length / 2
                                                      )
                                                    }
                                                    className="flex items-center gap-x-2"
                                                  >
                                                    {item?.status ? (
                                                      <Check
                                                        name="Check"
                                                        height={15}
                                                        width={15}
                                                        className="font-bold text-primary"
                                                      />
                                                    ) : (
                                                      <Dot
                                                        name="Check"
                                                        height={15}
                                                        width={15}
                                                        className="font-bold"
                                                      />
                                                    )}
                                                    <p>{item?.name}</p>
                                                  </div>
                                                ))}
                                            </div>
                                          </>
                                        )}
                                      </div>

                                      <div className="relative inline-block">
                                        <button
                                          className="button button_primary bg-primary hover:bg-secondary text-white font-bold rounded cursor-pointer w-full flex justify-center items-center whitespace-nowrap px-3 py-2 "
                                          onMouseEnter={() =>
                                            handleMouseEnter(application)
                                          }
                                          onMouseLeave={handleMouseLeave}
                                          type="button"
                                        >
                                          {applicationDetails?.isCompleted && (
                                            <p className="text-xs font-semibold">
                                              100%{" "}
                                              {staticContents?.application
                                                ?.completed || "Completed"}
                                            </p>
                                          )}
                                          {!applicationDetails?.isCompleted && (
                                            <p className="text-xs font-semibold">
                                              {
                                                applicationDetails?.completionPercentage
                                              }
                                              %{" "}
                                              {staticContents?.application
                                                ?.completed || "Completed"}
                                            </p>
                                          )}
                                        </button>
                                        {popoverVisible?.applicationId ===
                                          applicationDetails?.applicationId &&
                                          applicationDetails?.sections
                                            ?.length &&
                                          window.innerWidth <= 1279 && (
                                            <div className="absolute lg:w-[500px] md:w-[500px] xl:w-[500px] w-[250px]  z-[1000] bg-white border border-gray-300 rounded shadow-lg p-4 gap-4 flex flex-wrap  top-full mt-2 -right-10">
                                              <p className="font-bold text-base">
                                                Complete application with
                                                following steps
                                              </p>

                                              <div className="grid lg:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 grid-cols-1 gap-2 gap-x-12">
                                                {sortOrder(
                                                  applicationDetails?.sections,
                                                  "displayOrder"
                                                ).map(
                                                  (
                                                    section: any,
                                                    index: any
                                                  ) => (
                                                    <div
                                                      key={index}
                                                      className="flex gap-4"
                                                    >
                                                      <span
                                                        className={
                                                          "text-sm text-[#6a7580]"
                                                        }
                                                      >
                                                        {section.status ||
                                                        applicationDetails.isCompleted ? (
                                                          <Check
                                                            name="Check"
                                                            height={15}
                                                            width={15}
                                                            className="font-bold text-primary"
                                                          />
                                                        ) : (
                                                          <Dot
                                                            name="Check"
                                                            height={15}
                                                            width={15}
                                                            className="font-bold"
                                                          />
                                                        )}
                                                      </span>
                                                      <p className="text-sm text-[#6a7580]">
                                                        {section.name}
                                                      </p>
                                                    </div>
                                                  )
                                                )}
                                              </div>
                                            </div>
                                          )}
                                      </div>
                                    </div>

                                    <div className="flex flex-col md:flex-row items-center gap-2">
                                      {applicationDetails?.isCompleted && (
                                        <button
                                          className="button button_primary bg-primary hover:bg-secondary text-white font-bold min-h-[40px] rounded cursor-pointer w-full lg:w-[220px] p-2 flex justify-center items-center"
                                          onClick={async (e) => {
                                            e.preventDefault();
                                            setIsFetchingPdfUrl(true);

                                            const response =
                                              await getStudentDetails(
                                                nextFormDetails?.oap,
                                                userEmail,
                                                applicationDetails?.applicationId,
                                                apiKey,
                                                await getAccessToken()
                                              );

                                            setIsFetchingPdfUrl(false);

                                            if (response?.signedUrl) {
                                              window.open(
                                                response.signedUrl,
                                                "_blank"
                                              );
                                            }
                                          }}
                                        >
                                          {isFetchingPdfUrl ? (
                                            <Image
                                              priority
                                              src={loader}
                                              height={32}
                                              width={32}
                                              style={{ objectFit: "contain" }}
                                              alt="loader"
                                            />
                                          ) : (
                                            staticContents?.application
                                              ?.viewApplicationForm ||
                                            "View Application Form"
                                          )}
                                        </button>
                                      )}
                                      {showChangeRequestButton && (
                                        <Link
                                          href={`/change-request-form?opportunityId=${applicationDetails.applicationId}`}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="button button_secondary bg-secondary hover:bg-primary text-white font-bold min-h-[40px] rounded cursor-pointer w-full lg:w-[220px] p-2 flex justify-center items-center no-underline"
                                          style={getBrandSpecificFontStyle(
                                            fontSize,
                                            "label"
                                          )}
                                        >
                                          Change Request
                                        </Link>
                                      )}
                                    </div>
                                    {!applicationDetails?.isCompleted && (
                                      <button
                                        className="button button_primary bg-primary hover:bg-secondary text-white font-bold h-10 rounded cursor-pointer w-full lg:w-[200px] p-2 flex justify-center items-center"
                                        onClick={(e) => {
                                          e.preventDefault();

                                          handleContinueApplication(
                                            applicationDetails
                                          );
                                        }}
                                      >
                                        {staticContents?.application
                                          ?.continueApplication ||
                                          "Continue Application"}
                                      </button>
                                    )}
                                  </div>
                                );
                              }
                            )}
                          </div>
                        )}

                        <div className="form-action w-full flex justify-center "></div>
                      </form>
                    </div>
                  </div>
                ) : null}

                {labels?.length > 0 || formQuery?.fieldData?.length > 0 ? (
                  <div className="index-widget shadow-[0_3px_10px_rgb(0,0,0,0.2)] rounded p-[30px] lg:mb-[50px] sm:mb-0 md:mb-0">
                    <div className="index-widget-wrapper">
                      <form className="form" noValidate>
                        <div
                          className={`form-section w-full flex flex-col items-center gap-4`}
                        >
                          <h3 className="form-section-title text-primary font-semibold mb-2 text-xl">
                            {formQuery?.displayName}
                          </h3>

                          {formQuery.preQualificationResubmit && (
                            <div className="p-6 mb-4 bg-gray-100 flex justify-center w-[95%] text-center rounded-md">
                              <ReactMarkdown
                                className="markDown pl-9 pr-9"
                                components={{
                                  a: ({ href, children }) => (
                                    <a
                                      href={href}
                                      className="text-[#01B0E1] underline"
                                      onClick={handlePreQualificationResubmit}
                                    >
                                      {children}
                                    </a>
                                  ),
                                }}
                              >
                                {formQuery.preQualificationResubmit.text}
                              </ReactMarkdown>
                            </div>
                          )}

                          <div
                            className={`form-section-content justify-center flex flex-col lg:flex-row flex-wrap gap-4 items-center w-full font-thin
	                           ${
                               formQuery?.fieldData?.length - 1 === 2
                                 ? "h-auto lg:justify-center lg:ml-64"
                                 : "h-auto"
                             }`}
                          >
                            {formQuery?.fieldData?.map(
                              (item: any, i: number) => {
                                if (item?.type === "programCards") {
                                  return (
                                    <div
                                      className="bg-background w-full flex flex-wrap gap-4"
                                      key={i}
                                    >
                                      {labels?.map((data: any, index: any) => (
                                        <div
                                          key={index}
                                          style={{
                                            backgroundColor: "#f2f2f2",
                                            backgroundImage: `url(${formQuery?.layout?.backgroundImage})`,
                                            backgroundSize: "auto",
                                            backgroundRepeat: "no-repeat",
                                            backgroundPosition: "right center",
                                          }}
                                          className="border border-gray-300 p-4 rounded-lg flex flex-col lg:flex-row items-center w-full lg:w-[47%] justify-between"
                                        >
                                          <p className="text-lg font-bold mb-4 lg:mb-0">
                                            {data?.label}
                                          </p>
                                          <button
                                            className="button button_primary bg-[#212a33] hover:bg-secondary flex-shrink-0   text-white font-bold h-10 rounded cursor-pointer w-full lg:w-[150px] p-2 flex justify-center items-center"
                                            onClick={(e) => {
                                              e.preventDefault();
                                              setProgramDetails({
                                                programDisplayName: data?.label,
                                                program: data?.value,
                                                levelDisplayName: data?.level,
                                                level: data?.levelId,
                                              });
                                              handleShowModal(
                                                data?.label,
                                                data?.value,
                                                data?.level,
                                                data?.levelId
                                              );
                                              setIsOpen(true);
                                            }}
                                          >
                                            {item?.placeHolder}
                                          </button>
                                        </div>
                                      ))}
                                    </div>
                                  );
                                }
                                return (
                                  item.fieldName && (
                                    <div
                                      className={`w-full max-h-96 ${
                                        item?.type !== "programCards"
                                          ? "lg:w-[28%]"
                                          : ""
                                      }`}
                                      key={i}
                                    >
                                      <DynamicFields
                                        arrayIndex={i}
                                        getLookupData={
                                          dropdownOptions?.[item?.fieldName] ||
                                          []
                                        }
                                        watch={() => {}}
                                        fromApplicationFilter={true}
                                        register={register}
                                        fieldItem={item}
                                        fieldType={item?.type}
                                        fieldName={item?.fieldName}
                                        isMandatory={item?.required}
                                        label={item?.displayName}
                                        isVisibleWhen={!item?.visibleWhen}
                                        selectedValue={
                                          applicationFields?.[item?.fieldName]
                                        }
                                        handleValueChanged={async (
                                          value: any,
                                          type?: string
                                        ) => {
                                          const {
                                            value: val,
                                            label,
                                            ...rest
                                          } = value;
                                          setErrors({});
                                          setApplicationFields((prev: any) => {
                                            let tempObj = {
                                              [item?.fieldName]: value,
                                              [item?.fieldDisplayName]:
                                                value.label,
                                              item: object?.[i + 1],
                                            };
                                            if (item?.childField)
                                              tempObj[item?.childField] = null;
                                            return {
                                              ...prev,
                                              ...rest,
                                              ...tempObj,
                                            };
                                          });

                                          let currentNode = item;
                                          while (currentNode?.childField) {
                                            object.forEach(
                                              (element: any, index: number) => {
                                                if (
                                                  element?.fieldName ==
                                                  currentNode?.childField
                                                ) {
                                                  setApplicationFields(
                                                    (prev: any) => {
                                                      let tempObj: any = {};
                                                      if (element?.fieldName)
                                                        tempObj[
                                                          element?.fieldName
                                                        ] = null;
                                                      return {
                                                        ...prev,
                                                        ...tempObj,
                                                      };
                                                    }
                                                  );
                                                  currentNode = element;
                                                  return;
                                                }
                                              }
                                            );
                                          }
                                          setDropdownOptions((prev: any) => {
                                            return {
                                              ...prev,
                                              [item?.childField]: null,
                                            };
                                          });
                                        }}
                                        errorMessage={errors[item?.fieldName]}
                                        trigger={() => setErrors({})}
                                        isLoading={isLoading}
                                        required
                                      />
                                    </div>
                                  )
                                );
                              }
                            )}
                          </div>
                          {formQuery?.fieldData
                            ?.filter((item: any) => item.visibleWhen)
                            .map((ele: any, i: number) => {
                              return checkFieldVisibility(ele?.visibleWhen) ? (
                                <div
                                  key={i}
                                  className="mb-5 items-center lg:mx-16"
                                >
                                  <div className="max-w-none mb-2 text-sm">
                                    <p>{ele?.text}</p>
                                  </div>
                                </div>
                              ) : null;
                            })}
                        </div>
                        <div className="form-action w-full flex justify-center">
                          {formQuery?.fieldData
                            ?.filter((item: any) => item.type == "button")
                            .map((ele: any, i: number) => (
                              <div
                                key={i}
                                className="button button_primary bg-secondary hover:bg-primary w-full lg:w-[260px] mx-auto flex justify-center items-center text-white font-bold h-[41px] rounded cursor-pointer"
                                style={getBrandSpecificFontStyle(
                                  fontSize,
                                  "label"
                                )}
                                onClick={() => {
                                  // setSaving((prev: boolean) => !prev);
                                  const basicDetails: any = JSON.parse(
                                    localStorage.getItem("basic-details") as any
                                  );
                                  if (
                                    ele?.canHaveQuestionnarieFields &&
                                    questionariesSectionQuery?.allowedCountries?.includes(
                                      basicDetails[
                                        questionariesSectionQuery
                                          ?.allowedFieldName
                                      ]
                                    )
                                  ) {
                                    let temp =
                                      flattenPayload(applicationFields);

                                    let isValid = validateOutput(temp, object);

                                    if (isValid.length === 0) {
                                      setShowQuestionarrieModal(true);
                                    }
                                  } else {
                                    setApplicationFields((prev: any) => {
                                      return {
                                        ...prev,
                                      };
                                    });

                                    handleSaveApplication();
                                  }
                                }}
                              >
                                {saving ? (
                                  <div className=" w-full  flex items-center justify-center">
                                    <Image
                                      priority
                                      src={loader2}
                                      height={20}
                                      width={20}
                                      alt="Follow us on Twitter"
                                    />
                                  </div>
                                ) : (
                                  ele.placeholder // start application
                                )}
                              </div>
                            ))}
                        </div>
                      </form>
                    </div>
                  </div>
                ) : null}
              </LayoutWrapper>
            </div>
          </div>
          <div>
            <Modal
              isOpen={modalIsOpen}
              className="fixed inset-0 flex items-center justify-center p-4"
            >
              <div className="bg-white w-full max-w-xl p-6 rounded-lg shadow-lg overflow-y-auto max-h-[90vh]">
                <div className="flex flex-col items-center flex-wrap gap-4">
                  {getPopupDetails(formQuery)?.map((item: any, i: number) => {
                    return (
                      <div
                        className={`w-full max-h-96 ${
                          item?.type !== "programCards" ? "" : ""
                        }`}
                        key={i}
                      >
                        <DynamicFields
                          arrayIndex={i}
                          getLookupData={
                            dropdownOptions?.[item?.fieldName] || []
                          }
                          watch={() => {}}
                          fromApplicationFilter={true}
                          register={register}
                          fieldItem={item}
                          fieldType={item?.type}
                          fieldName={item?.fieldName}
                          isMandatory={item?.required}
                          label={item?.displayName}
                          isVisibleWhen={checkFieldVisibility(
                            item?.visibleWhen
                          )}
                          selectedValue={applicationFields?.[item?.fieldName]}
                          handleValueChanged={async (
                            value: any,
                            type?: string
                          ) => {
                            const { value: val, label, ...rest } = value;

                            setErrors({});
                            setApplicationFields((prev: any) => {
                              let tempObj = {
                                [item?.fieldName]: value,
                                [item?.fieldDisplayName]: value.label,
                                item: getPopupDetails(formQuery)?.[i + 1],
                              };

                              if (item?.resetChild)
                                tempObj[item?.resetChild] = null;
                              return { ...prev, ...tempObj };
                            });

                            let currentNode = item;
                            while (currentNode?.childField) {
                              getPopupDetails(formQuery)?.forEach(
                                (element: any, index: number) => {
                                  if (
                                    element?.fieldName ==
                                    currentNode?.childField
                                  ) {
                                    setApplicationFields((prev: any) => {
                                      let tempObj: any = {};
                                      if (element?.fieldName)
                                        tempObj[element?.fieldName] = null;
                                      return { ...prev, ...tempObj };
                                    });
                                    currentNode = element;
                                    return;
                                  }
                                }
                              );
                            }
                            setDropdownOptions((prev: any) => {
                              return {
                                ...prev,
                                [item?.childField]: null,
                              };
                            });
                          }}
                          errorMessage={errors[item?.fieldName]}
                          trigger={() => setErrors({})}
                          isLoading={isLoading}
                          required
                        />
                      </div>
                    );
                  })}
                </div>

                <div className="form-action w-full flex justify-center gap-4 mt-6">
                  {getPopupDetails(formQuery)
                    ?.filter((item: any) => item.type == "button")
                    .map((ele: any, i: number) => (
                      <button
                        key={i}
                        className="button button_primary bg-secondary hover:bg-primary w-full lg:w-[260px] mx-auto flex justify-center items-center text-white font-bold h-[41px] rounded cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-colors"
                        onClick={() => {
                          let temp = flattenPayload(applicationFields);
                          const isValid = validateOutput(
                            temp,
                            formQuery?.popupDetails?.fieldData
                          );
                          if (isValid.length) {
                            return;
                          }
                          handleSaveApplication();
                        }}
                      >
                        {saving ? (
                          <div className="w-full flex items-center justify-center">
                            <Image
                              priority
                              src={loader2}
                              height={20}
                              width={20}
                              alt="Loading"
                            />
                          </div>
                        ) : (
                          ele.placeholder // submit
                        )}
                      </button>
                    ))}
                  <button
                    onClick={closeModal}
                    className="button button_primary bg-secondary hover:bg-primary w-full lg:w-[260px] mx-auto flex justify-center items-center text-white font-bold h-[41px] rounded cursor-pointer"
                  >
                    Close
                  </button>
                </div>
              </div>
            </Modal>
            {showQuestionarrieModal ? (
              <QuestionnairePopup
                isOpen={showQuestionarrieModal}
                onClose={() => {
                  setShowQuestionarrieModal(false);
                }}
                fieldData={questionariesSectionQuery?.fieldData}
                questionariesSectionQuery={questionariesSectionQuery}
                onSubmit={(data: any) => handleSaveApplication(data)}
                onCancelationApplication={(data: any) =>
                  handleCancelationApplication(data)
                }
                basicDetails={applicationFields}
                sectionQuery={sectionQuery}
              />
            ) : null}
          </div>
        </div>
      )}
    </>
  );
};

export default ApplicationFilter;
