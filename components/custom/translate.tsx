import React, { use<PERSON>em<PERSON>, use<PERSON><PERSON>back, useEffect } from "react";
import Select, { components } from "react-select";
import Image from "next/image";
import { useAtom } from "jotai";
import {
  preferredLanguage,
  consumerAPIKey,
  staticContentsAtom,
  email,
} from "@/lib/atom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getOapDetail, saveStudentInfo } from "@/api/api";

interface LanguageSelectorProps {
  languageData: any;
}
interface LanguageOption {
  value: string;
  label: string;
  flag: string;
}

// Memoized Option component to prevent re-renders
const Option = React.memo(({ children, ...props }: any) => (
  <components.Option {...props}>
    <div className="flex items-center gap-2">
      {props && props.data.flag && (
        <Image
          src={props.data.flag}
          alt={`${props.data.label} Flag`}
          width={24}
          height={24}
          className="w-6 h-4 object-contain"
          priority
          unoptimized
        />
      )}
      {children}
    </div>
  </components.Option>
));
Option.displayName = "Option";

// Memoized SingleValue component to prevent re-renders
const SingleValue = React.memo(({ children, ...props }: any) => (
  <components.SingleValue {...props}>
    <div className="flex items-center gap-2">
      {props.data.flag && (
        <Image
          src={props.data.flag}
          alt={`${props.data.label} Flag`}
          width={24}
          height={24}
          className="w-6 h-4 object-contain"
          priority
          unoptimized
        />
      )}
      {children}
    </div>
  </components.SingleValue>
));
SingleValue.displayName = "SingleValue";

const LanguageSelector = ({ languageData }: LanguageSelectorProps) => {
  const [selectedLanguage, setSelectedLanguage] = useAtom(preferredLanguage);
  const [apiKey] = useAtom(consumerAPIKey);
  const [, setStaticContents] = useAtom(staticContentsAtom);

  // Memoize pageDetails to prevent recreation on every render
  const pageDetails = useMemo(
    () => ({
      screen: process.env.NEXT_PUBLIC_OAP_NAME,
      mode: process.env.NEXT_PUBLIC_OAP_MODE,
    }),
    []
  );
  const [userEmail] = useAtom(email);

  const queryClient = useQueryClient();

  useEffect(() => {
    setSelectedLanguage(selectedLanguage || "en");
  }, []);

  // Include selectedLanguage in the query key to ensure re-fetch on language change
  const { refetch: refetchPageQuery } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`, selectedLanguage],
    queryFn: async () => {
      console.log("Fetching with language:", selectedLanguage);
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
          ...(selectedLanguage === "de" && { language: "de" }),
        },
        apiKey
      );

      if (res?.staticContents) {
        console.log("Setting static contents from query response");
        setStaticContents(res.staticContents);
      }

      return res;
    },
    enabled: !!apiKey,
    // Prevent unnecessary refetches
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    // Consider data fresh for 5 minutes
  });

  // Memoize the language change handler
  const handleLanguageChange = useCallback(
    async (option: LanguageOption | null) => {
      if (option && option.value !== selectedLanguage) {
        console.log("Language changing to:", option.value);

        // Update the language state - this will trigger a re-fetch automatically
        // because selectedLanguage is part of the query key
        setSelectedLanguage(option.value);

        if (userEmail) {
          await saveStudentInfo(
            { email: userEmail, localization: option.value },
            apiKey,
            {
              oapName: pageDetails?.screen,
            }
          );
        }

        queryClient.invalidateQueries({
          queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
        });

        // Then trigger the refetch with the new language context
        setTimeout(() => {
          refetchPageQuery();
        }, 0);

        // No need for additional invalidations or refetches here
        // as the query will automatically re-run due to the key change
      }
    },
    [
      selectedLanguage,
      setSelectedLanguage,
      queryClient,
      pageDetails,
      refetchPageQuery,
    ]
  );

  // Memoize languageWithFlags to prevent recreation on every render
  const languageWithFlags = useMemo(() => {
    return languageData?.languages.map((lang: any) => ({
      ...lang,
      flag: languageData?.flagInfo?.find(
        (flag: any, index: number) =>
          index ===
          languageData.languages.findIndex((l: any) => l.value === lang.value)
      )?.signedUrl,
    }));
  }, [languageData?.languages, languageData?.flagInfo]);

  // Memoize the selected value to prevent unnecessary recalculations
  const selectedValue = useMemo(() => {
    return (
      languageWithFlags?.find((lang: any) => lang.value === selectedLanguage) ||
      languageWithFlags?.[0]
    );
  }, [languageWithFlags, selectedLanguage]);

  // Memoize custom components object to prevent recreation
  const customComponents = useMemo(() => ({ Option, SingleValue }), []);

  // Memoize custom styles to prevent recreation on every render
  const customStyles = useMemo(
    () => ({
      control: (base: any) => ({
        ...base,
        ...(languageData?.styles || {}),
        // Let CSS handle focus-visible styles
        outline: "none",
      }),
      menu: (base: any) =>
        ({
          ...base,
          backgroundColor: "white",
          borderRadius: "0.5rem",
          marginTop: "4px",
          overflow: "hidden",
          boxShadow:
            "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        } as any),
      option: (base: any, state: any) =>
        ({
          ...base,
          backgroundColor: state.isFocused ? "#F5F5F5" : "white",
          color: "#1F2937",
          cursor: "pointer",
          padding: "10px 12px",
          "&:active": {
            backgroundColor: "#F5F5F5",
          },
          fontSize: "1rem",
        } as any),
      singleValue: (base: any) =>
        ({
          ...base,
          color: "#1F2937",
          textAlign: "center",
          width: "100%",
        } as any),
      dropdownIndicator: (base: any) =>
        ({
          ...base,
          color: "#6B7280",
          padding: "4px",
          "&:hover": {
            color: "#374151",
          },
        } as any),
      indicatorSeparator: () =>
        ({
          display: "none",
        } as any),
      valueContainer: (base: any) =>
        ({
          ...base,
          padding: "2px 4px",
          justifyContent: "center",
        } as any),
    }),
    [languageData?.styles]
  );

  return (
    <Select
      options={languageWithFlags || languageData?.languages}
      value={selectedValue}
      onChange={handleLanguageChange}
      menuPlacement="auto"
      components={customComponents}
      isSearchable={false}
      styles={customStyles}
      aria-label="Select language"
      tabSelectsValue={true}
      openMenuOnFocus={true}
      closeMenuOnSelect={true}
      classNamePrefix="react-select"
    />
  );
};

export default React.memo(LanguageSelector);
