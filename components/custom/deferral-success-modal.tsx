import React from "react";
import {
  Alert<PERSON><PERSON>og,
  AlertDialog<PERSON><PERSON>,
  AlertDialog<PERSON>ontent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Check } from "lucide-react";

interface DeferralSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReturnToDeferral: () => void;
  successMessages?: {
    title?: string;
    message?: string;
    buttonText?: string;
  };
  submissionData?: {
    changeRequestId: string;
  };
}

const DeferralSuccessModal: React.FC<DeferralSuccessModalProps> = ({
  isOpen,
  onClose,
  onReturnToDeferral,
  successMessages,
  submissionData,
}) => {
  const title = successMessages?.title || "Request Submitted Successfully";
  const message = successMessages?.message || `Your request has been submitted successfully.`; //Reference ID: ${submissionData?.changeRequestId || 'N/A'}
  const buttonText = successMessages?.buttonText || "Return to Change Request";

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md border-0 shadow-lg">
        <div className="bg-primary h-2 w-full absolute top-0 left-0 rounded-t-lg"></div>
        <AlertDialogHeader className="flex flex-col items-center pt-6">
          <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
            <Check className="h-8 w-8 text-green-600" />
          </div>
          <AlertDialogTitle className="text-xl font-semibold text-center text-gray-800">
            {title}
          </AlertDialogTitle>
          <div className="mt-2 text-center text-gray-600">
            {message}
          </div>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex justify-center sm:justify-center mt-4">
          <AlertDialogAction
            onClick={onReturnToDeferral}
            className="bg-primary hover:bg-secondary text-white font-medium px-8"
          >
            {buttonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeferralSuccessModal;
