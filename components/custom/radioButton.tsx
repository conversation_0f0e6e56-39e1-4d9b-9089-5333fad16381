import { Label } from "@/components/ui/label";
import * as RadioGroup from "@radix-ui/react-radio-group";

interface RadioButtonProps {
  selectedValue: string;
  handleChange: Function;
  register: any;
  id: string;
  options?: Array<{ value: string; label: string }> | Array<string>;
  defaultValue?: any;
  errorMessage?: any;
  isLoading?: boolean;
}

export function RadioButton(props: RadioButtonProps) {
  const {
    handleChange,
    register,
    options,
    selectedValue,
    errorMessage,
    isLoading,
  } = props;
  const { onChange, onBlur, ref } = register;

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <RadioGroup.Root
      className="RadioGroupRoot"
      value={selectedValue}
      aria-label="View density"
      onValueChange={(value: any) => handleChange(value)}
      onClick={(e) => e.preventDefault()}
      onBlur={onBlur}
      onChange={onChange}
    >
      {options?.map((item: any, index: number) => (
        <div key={index} style={{ display: "flex", alignItems: "center" }}>
          <RadioGroup.Item
            className={`RadioGroupItem border ${
              errorMessage ? "border-error" : "border-border"
            }  rounded-full h-4 w-4 mx-2`}
            value={item.value || item}
            id={`r${index}`}
          >
            <RadioGroup.Indicator className="RadioGroupIndicator border border-border rounded-full" />
          </RadioGroup.Item>
          <Label className={`Label`} htmlFor={`r${index}`}>
            {item.displayText || item.label || item}
          </Label>
        </div>
      ))}
    </RadioGroup.Root>
  );
}

export const PopUpRadioButton = ({
  id,
  selectedValue,
  handleChange,
  options,
  register,
  errorMessage,
  onBlur,
  onChange,
}: any) => {
  return (
    <RadioGroup.Root
      className="RadioGroupRoot space-y-3"
      value={selectedValue}
      aria-label="View density"
      onValueChange={(value: any) => handleChange(value)}
      onClick={(e) => e.preventDefault()}
      onBlur={onBlur}
      onChange={onChange}
    >
      {options?.map((item: any, index: number) => (
        <div
          key={index}
          className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
        >
          <RadioGroup.Item
            className={`RadioGroupItem border ${
              errorMessage ? "border-error" : "border-border"
            } rounded-full h-5 w-5 flex items-center justify-center bg-white`}
            value={item.value || item}
            id={`r${index}`}
          >
            <RadioGroup.Indicator className="RadioGroupIndicator w-2.5 h-2.5 bg-primary rounded-full" />
          </RadioGroup.Item>
          <Label
            className="text-sm font-medium cursor-pointer flex-1"
            htmlFor={`r${index}`}
          >
            {item.label || item}
          </Label>
        </div>
      ))}
      {errorMessage && (
        <p className="text-error text-sm mt-1">{errorMessage}</p>
      )}
    </RadioGroup.Root>
  );
};
