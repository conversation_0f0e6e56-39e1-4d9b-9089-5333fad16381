# OAP Frontend Application

This is a [Next.js](https://nextjs.org/) project for the Online Application Portal (OAP) frontend, bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Architecture Overview

### Centralized Visibility Logic System

The application features a centralized visibility logic system that controls when form fields, sections, and components should be displayed based on dynamic conditions. This system replaces scattered `checkVisibility` implementations across the application.

#### Core Files

- `lib/visibilityUtils.ts` - Core visibility logic and evaluation engine
- `hooks/useVisibility.ts` - React hooks for easy integration
- `VISIBLE_WHEN_LOGIC.md` - Detailed documentation and usage examples

#### Key Functions

**`evaluateConditions(conditionSet, formData)`**

- Core evaluation engine that processes visibility conditions against form data
- Supports complex logical operations (AND, OR, NOT) and various comparison types
- Works with both single conditions and arrays of conditions

**`checkVisibility(visibleWhenProps, watch, additionalData)`**

- React Hook Form compatible wrapper
- Automatically extracts field names and creates form data from watch functions
- Provides fallback support for additional data sources

#### Supported Condition Types

| Condition          | Description                      | Example                                                             |
| ------------------ | -------------------------------- | ------------------------------------------------------------------- |
| `equals` (default) | Field equals value               | `{ fieldName: 'type', value: 'student' }`                           |
| `notEqual`         | Field not equal to value         | `{ fieldName: 'status', value: 'inactive', condition: 'notEqual' }` |
| `exists`           | Field has any value              | `{ fieldName: 'optional', condition: 'exists' }`                    |
| `notExists`        | Field is empty/null              | `{ fieldName: 'temp', condition: 'notExists' }`                     |
| `contains`         | Contains text (case-insensitive) | `{ fieldName: 'desc', value: 'important', condition: 'contains' }`  |
| `notContains`      | Doesn't contain text             | `{ fieldName: 'desc', value: 'spam', condition: 'notContains' }`    |
| `greaterThan`      | Numeric/date comparison          | `{ fieldName: 'age', value: 18, condition: 'greaterThan' }`         |
| `lessThan`         | Numeric/date comparison          | `{ fieldName: 'score', value: 100, condition: 'lessThan' }`         |
| `and`              | All rules must be true           | `{ condition: 'and', rules: [...] }`                                |
| `notAnd`           | Not all rules are true           | `{ condition: 'notAnd', rules: [...] }`                             |
| `or`               | Any condition is true            | `{ logic: 'or', conditions: [...] }`                                |

#### Usage Examples

**Basic Usage:**

```tsx
import { checkVisibility } from "@/lib/visibilityUtils";

const isVisible = checkVisibility(
  { fieldName: "userType", value: "student" },
  watch
);
```

**Complex OR Logic:**

```tsx
const orCondition = {
  logic: "or",
  conditions: [
    { fieldName: "level", value: "graduate" },
    { fieldName: "hasExperience", value: true },
    { fieldName: "age", value: 25, condition: "greaterThan" },
  ],
};
```

**AND Logic with Multiple Rules:**

```tsx
const andCondition = {
  condition: "and",
  rules: [
    { fieldName: "age", value: 18, condition: "greaterThan" },
    { fieldName: "country", value: "US" },
    { fieldName: "hasVisa", value: true },
  ],
};
```

#### Components Using Centralized System

The following components have been migrated to use the centralized visibility system:

- ✅ `components/custom/form-container.tsx`
- ✅ `components/Forms/applicationFilter.tsx`
- ✅ `components/Forms/genericRequestForm.tsx`
- ✅ `components/Forms/custom-filter/uegApplicationFilter.tsx`
- ✅ `components/custom/DynamicFields.tsx`
- ✅ `components/Forms/sectionFrom.tsx`

#### Benefits

- **Code Reduction**: Eliminated 500+ lines of duplicate visibility logic
- **Single Source of Truth**: All condition evaluation happens in one place
- **Consistent Behavior**: Same logic across all components
- **Performance**: Optimized evaluation with proven algorithms
- **Maintainability**: Bug fixes and new features added centrally
- **100% Backward Compatible**: All existing `visibleWhen` props continue to work

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
