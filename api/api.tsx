import { fetchAuthSession } from "aws-amplify/auth";

interface RequestOptions {
  method?: string;
  headers: any;
  body: any;
}
export const apiCall = async (url: string, options: any, queryParams?: any) => {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_OAP_BACKEND_URL;
    const queryString = new URLSearchParams(queryParams).toString();

    let apiUrlWithParams = `${apiUrl}/${url}`;

    if (queryString) {
      if (apiUrlWithParams.includes("?")) {
        apiUrlWithParams += `&${queryString}`;
      } else {
        apiUrlWithParams += `?${queryString}`;
      }
    }

    const requestOptions: RequestOptions = {
      method: options?.method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(options?.payload),
    };
    if (options?.key) {
      requestOptions.headers["x-api-key"] = options?.key;
    }
    if (options?.accessToken) {
      requestOptions.headers["Authorization"] = options?.accessToken;
    }
    const response = await fetch(apiUrlWithParams, {
      ...requestOptions,
      cache: "no-store",
    });

    try {
      const data = await response.json();
      if (data) {
        return data;
      }
    } catch (error) {
      if (response.status === 401 || response.status === 403) {
        window.location.pathname = "/login";
      }
      return response;
    }
  } catch (error) {
    throw error;
  }
};

export const getLookUpData = async (
  payload: any,
  key: string,
  accessToken?: string,
  queryParams?: any
) => {
  const routeName = `${payload?.name}`;
  const path = accessToken ? `student/${routeName}` : routeName;
  return await apiCall(path, { method: "GET", key, accessToken }, queryParams);
};

export const getOapDetail = async (
  queryParams: any,
  key?: string,
  accessToken?: string
) => {
  const path = accessToken ? `student/oap` : `oap`;
  return apiCall(path, { method: "GET", key, accessToken }, queryParams);
};

export const getOapForm = async (
  queryParams: any,
  key: string,
  accessToken?: string
) => {
  const path = accessToken ? `student/oap/forms` : `oap/forms`;
  return apiCall(path, { method: "GET", key, accessToken }, queryParams);
};

export const getOapFormSections = async (
  queryParams: any,
  key: string,
  accessToken?: string
) => {
  const path = accessToken ? `student/oap/form/sections` : `oap/form/sections`;
  return apiCall(path, { method: "GET", key, accessToken }, queryParams);
};

export const getStudentDetailsById = async (
  queryParams: any,
  key: string,
  accessToken?: string
) => {
  const path = accessToken
    ? `student/oap/getstudentdetailsbyid`
    : `oap/getstudentdetailsbyid`;
  return apiCall(path, { method: "GET", key, accessToken }, queryParams);
};

export const getStudentApplications = async (
  queryParams: any,
  key: string,
  accessToken?: string
) => {
  return apiCall(
    "student/oap/getstudentapplications",
    { method: "GET", key, accessToken },
    queryParams
  );
};

export const getStudentInfo = async (queryParams: any, key: string) => {
  return await apiCall("oap/studentinfo", { method: "GET", key }, queryParams);
};

export const getStudentDetails = async (
  oap: string,
  email: string,
  applicationId: string,
  key: string,
  accessToken?: string
) => {
  return apiCall(
    "student/oap/getstudentdetails",
    { method: "GET", key, accessToken },
    {
      oapName: oap,
      email,
      applicationId,
    }
  );
};

export const saveOapForm = async (
  payload: any,
  queryParams: any,
  key: string,
  accessToken?: string
) => {
  return apiCall(
    "student/oap/savestudentdetails",
    { method: "POST", payload, key, accessToken },
    queryParams
  );
};

export const getPreSignedURL = async (
  payload: any,
  key: string,
  accessToken?: string
) => {
  return apiCall("student/oap/uploadstudentdocument/getsignedurl", {
    method: "POST",
    payload,
    key,
    accessToken,
  });
};

export const getDocumentName = async (
  queryParams: any,
  key: string,
  accessToken?: string
) => {
  return apiCall(
    "student/oap/getstudentdocument",
    { method: "GET", key, accessToken },
    queryParams
  );
};

export const deleteDocument = async (
  queryParams: any,
  key: string,
  accessToken?: string
) => {
  return apiCall(
    "student/oap/deletestudentdocument",
    { method: "DELETE", key, accessToken },
    queryParams
  );
};

export const getUpdatedDoc = async (
  payload: any,
  key: string,
  accessToken?: string
) => {
  return apiCall("student/oap/uploadstudentdocument", {
    method: "POST",
    payload,
    key,
    accessToken,
  });
};

export const getAllSectionForms = async (
  queryParams: any,
  key?: string,
  accessToken?: string
) => {
  return apiCall(
    "student/oap/sections",
    { method: "GET", key, accessToken },
    queryParams
  );
};

export const getAccessToken = async () => {
  try {
    const { tokens } = await fetchAuthSession();
    return tokens?.accessToken.toString();
  } catch {
    window.location.pathname = "/login";
    return;
  }
};

export const saveStudentInfo = async (
  payload: any,
  key: string,
  queryParams: any
) => {
  return await apiCall(
    "oap/savestudentInfo",
    {
      method: "POST",
      payload,
      key,
    },
    queryParams
  );
};

/**
 * Verify reCAPTCHA token before processing authentication requests
 * @param recaptchaToken - The reCAPTCHA token to verify
 * @param action - The action being performed (login, signup, etc.)
 * @param apiKey - The API key for the brand
 * @returns Promise<boolean> - Whether the token is valid
 */
export const verifyRecaptcha = async (
  recaptchaToken: string,
  action: string,
  apiKey: string
): Promise<{ success: boolean; score?: number; error?: string }> => {
  try {
    return await apiCall("oap/verify-recaptcha", {
      method: "POST",
      payload: {
        token: recaptchaToken,
        action: action,
      },
      key: apiKey,
    });
  } catch (error: any) {
    console.log("Error verifying reCAPTCHA on api:", error);
    return {
      success: false,
      error: (error?.error as any) || "Failed to verify reCAPTCHA",
    };
  }
};

export const getOpportunityDetails = async (
  applicationFormId: string,
  key?: string,
  queryParams?: any
) => {
  // Use the oap/opportunity endpoint with ApplicationFormId__c filter
  return apiCall(
    `oap/opportunity/${applicationFormId}?filterBy=ApplicationFormId__c`,
    { method: "GET", key },
    queryParams
  );
};

export const submitChangeRequest = async (
  payload: any,
  queryParams: any,
  key: string
) => {
  return apiCall(
    `oap/submitchangerequest`,
    { method: "POST", payload, key },
    queryParams
  );
};
