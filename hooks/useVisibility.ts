import { useMemo } from "react";
import { checkVisibility, evaluateConditions } from "@/lib/visibilityUtils";

/**
 * Simple React hook for evaluating field visibility
 * @param visibleWhen - Visibility condition object
 * @param getValues - React Hook Form getValues function
 * @param watch - React Hook Form watch function (fallback)
 * @returns boolean - Whether the field should be visible
 */
export function useVisibility(
  visibleWhen: any,
  getValues: () => any,
  watch?: any
): boolean {
  return useMemo(() => {
    return checkVisibility(visibleWhen, watch);
  }, [visibleWhen, watch]);
}

/**
 * Hook for evaluating visibility with static form data (non-React Hook Form)
 * @param visibleWhen - Visibility condition object
 * @param formData - Object containing field-value pairs
 * @returns boolean - Whether the field should be visible
 */
export function useVisibilityWithData(
  visibleWhen: any,
  formData: Record<string, any>
): boolean {
  return useMemo(() => {
    return evaluateConditions(visibleWhen, formData);
  }, [visibleWhen, formData]);
}
