"use client";
import { useSearchParams } from "next/navigation";
import RequestTypeModal from "@/components/custom/request-type-modal";
import { extractApplicationId } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { getOapDetail } from "@/api/api";
import { useState } from "react";
import {
  dateReplacement,
  preferredDateFormat,
  requestTypeDataAtom,
  staticContentsAtom,
} from "@/lib/atom";
import { useAtom } from "jotai";
import { consumerAPIKey } from "@/lib/atom";
import { AlertCircle, AlertTriangle, Loader2 } from "lucide-react";

export default function DeferralPage() {
  const searchParams = useSearchParams();
  const applicationFormId = extractApplicationId(searchParams);
  const [pageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const [, setStaticContents] = useAtom(staticContentsAtom);
  const [, setPreferredDateFormat] = useAtom(preferredDateFormat);
  const [, setDateReplacement] = useAtom(dateReplacement);
  const [, setApiKey] = useAtom(consumerAPIKey);
  const [, setRequestTypeData] = useAtom(requestTypeDataAtom);

  const { data: pageQuery } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail({
        name: pageDetails.screen,
        mode: pageDetails.mode,
      });
      setStaticContents(res?.staticContents);
      setPreferredDateFormat(res?.preferedDateFormat);
      setDateReplacement(res?.replaceWith);
      setApiKey(res?.eipConsumerKey);
      setRequestTypeData(res?.requestTypes);
      return res;
    },
    enabled: true,
  });

  if (!applicationFormId) {
    return (
      <div className="flex w-full bg-background min-h-screen items-center justify-center">
        <div className="w-full max-w-lg p-6 bg-background border border-gray-200 shadow-lg sm:p-8 flex flex-col items-center gap-4 rounded-lg">
          <div className="w-16 h-16 rounded-full bg-error/10 flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-error" />
          </div>
          <div className="text-center">
            <h1 className="text-xl font-semibold text-gray-800 mb-2">
              Application ID Required
            </h1>
            <p className="text-gray-600">
              An opportunity ID is required to access this page. Please ensure
              you have the correct URL with a valid opportunity ID parameter.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (pageQuery && !pageQuery?.requestTypes) {
    return (
      <div className="flex w-full bg-background min-h-screen items-center justify-center p-4">
        <div
          id="request-types-not-found"
          className="bg-background border border-gray-200 rounded-2xl shadow-xl p-8 max-w-md text-center"
        >
          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-orange-100 to-orange-50 flex items-center justify-center mx-auto mb-6 shadow-inner">
            <AlertTriangle className="h-10 w-10 text-orange-600 drop-shadow-sm" />
          </div>
          <h1 className="text-2xl font-bold mb-3 text-gray-800">
            Request Types Not Found
          </h1>
          <p className="text-gray-600 mb-6 leading-relaxed">
            We couldn’t load the available request types. This might be a
            temporary issue.
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full px-6 py-3 rounded-lg bg-primary text-white hover:bg-primary/90 transition-colors duration-200 font-medium shadow-sm hover:shadow-md"
            >
              <Loader2 className="h-4 w-4 mr-2 inline" />
              Retry Loading
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-full bg-background min-h-screen">
      <RequestTypeModal
        applicationFormId={applicationFormId}
        requestTypes={pageQuery?.requestTypes}
      />
    </div>
  );
}
