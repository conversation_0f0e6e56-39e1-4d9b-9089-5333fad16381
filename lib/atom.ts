import { atomWithStorage } from "jotai/utils";

// Define the structure of static contents
interface StaticContents {
  errors?: {
    userValidation?: {
      passwordMismatch?: string;
      passwordRequirements?: string;
      [key: string]: any;
    };
    [key: string]: any;
  };
  [key: string]: any;
}

export const nextForm = atomWithStorage("next-form", {});
export const email = atomWithStorage("email", "");
export const applicationId = atomWithStorage("applicationId", "");
export const routes = atomWithStorage("routes", "");
export const brandLogo = atomWithStorage("brandLogo", "");
export const programmeName = atomWithStorage("programmeName", "");
export const consumerAPIKey = atomWithStorage("consumerAPIKey", "");
export const preferredDateFormat = atomWithStorage("preferredDateFormat", "");
export const qualifyingQuestions = atomWithStorage("qualifying-questions", {});
export const dateReplacement = atomWithStorage("dateReplacement", "");
export const preferredLanguage = atomWithStorage("preferredLanguage", "en");
export const staticContentsAtom = atomWithStorage<StaticContents>(
  "staticContents",
  {}
);
export const requestTypeDataAtom = atomWithStorage(
  "requestTypeData",
  {} as any
);
export const recaptchaEnabledAtom = atomWithStorage("recaptchaEnabled", false);

export const fontSizeAtom = atomWithStorage<any>("fontSize", {});

// AppHero state atom using session storage
const sessionStorageCustom = {
  getItem: (key: string) => {
    if (typeof window === "undefined") return null;
    const value = window.sessionStorage.getItem(key);
    if (value === null) return null;
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  },
  setItem: (key: string, value: boolean) => {
    if (typeof window === "undefined") return;
    window.sessionStorage.setItem(key, JSON.stringify(value));
  },
  removeItem: (key: string) => {
    if (typeof window === "undefined") return;
    window.sessionStorage.removeItem(key);
  },
};

export const appHeroAtom = atomWithStorage(
  "appHero",
  false,
  sessionStorageCustom
);
