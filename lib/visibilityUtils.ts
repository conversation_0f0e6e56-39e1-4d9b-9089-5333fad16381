/**
 * Centralized VisibleWhen Logic Utility
 *
 * This utility provides the evaluateConditions function to replace scattered
 * checkVisibility implementations across the application.
 *

/**
 * Evaluates a set of conditions against provided input data.
 * Supports condition types: equals, notEqual, exists, notExists, contains, notContains, greaterThan, lessThan, and, notAnd, or.
 * @param {Array|Object} conditionSet - Array of conditions or a single condition object from JSON
 * @param {Object} formData - Object containing field-value pairs to evaluate against
 * @returns {boolean} - True if all conditions are satisfied, false otherwise
 */
export function evaluateConditions(
  conditionSet: any,
  formData: Record<string, any>
): boolean {
  // Return true if no conditions are specified
  if (!conditionSet) {
    return true;
  }

  // Normalize input to an array for consistent processing
  const conditions = Array.isArray(conditionSet)
    ? conditionSet
    : [conditionSet];

  // Evaluate all conditions, requiring all to be true (logical AND)
  return conditions.every((condition) => {
    // Handle complex conditions with nested rules (and, notAnd, or)
    if (condition.condition === "and" || condition.condition === "notAnd") {
      const rulesSatisfied = condition.rules.every((rule: any) =>
        evaluateSingleCondition(rule, formData)
      );
      return condition.condition === "and" ? rulesSatisfied : !rulesSatisfied;
    }

    // Handle OR logic for grouped conditions
    if (condition.conditions && condition.logic === "or") {
      return condition.conditions.some((subCondition: any) =>
        evaluateSingleCondition(subCondition, formData)
      );
    }

    // Evaluate a single condition
    return evaluateSingleCondition(condition, formData);
  });
}

/**
 * Evaluates a single condition against input data.
 * @param {Object} condition - Single condition object with fieldName, value, and optional condition/operator
 * @param {Object} formData - Object containing field-value pairs
 * @returns {boolean} - True if the condition is satisfied, false otherwise
 */
function evaluateSingleCondition(
  condition: any,
  formData: Record<string, any>
): boolean {
  const { fieldName, value, condition: conditionType, operator } = condition;
  const inputValue = formData[fieldName];

  // Default to 'equals' if no condition or operator is specified
  const evalType = conditionType || operator || "equals";

  switch (evalType) {
    case "equals":
    case "equal":
      if (Array.isArray(value)) {
        return Array.isArray(inputValue)
          ? value.every((val) => inputValue.includes(val))
          : value.includes(inputValue);
      }
      return inputValue === value;

    case "notEqual":
      if (Array.isArray(value)) {
        return Array.isArray(inputValue)
          ? !value.every((val) => inputValue.includes(val))
          : !value.includes(inputValue);
      }
      return inputValue !== value;

    case "contains":
      if (Array.isArray(inputValue)) {
        return inputValue.some((val) =>
          String(val).toLowerCase().includes(String(value).toLowerCase())
        );
      }
      return String(inputValue)
        .toLowerCase()
        .includes(String(value).toLowerCase());

    case "notContains":
      if (Array.isArray(inputValue)) {
        return !inputValue.some((val) =>
          String(val).toLowerCase().includes(String(value).toLowerCase())
        );
      }
      return !String(inputValue)
        .toLowerCase()
        .includes(String(value).toLowerCase());

    case "exists":
      return inputValue !== undefined && inputValue !== null;

    case "notExists":
      return inputValue === undefined || inputValue === null;

    case "greaterThan":
      if (
        inputValue === undefined ||
        inputValue === null ||
        value === undefined ||
        value === null
      ) {
        return false;
      }
      // Convert to numbers or dates for comparison
      const inputNum = isNaN(inputValue)
        ? new Date(inputValue).getTime()
        : Number(inputValue);
      const conditionNum = isNaN(value)
        ? new Date(value).getTime()
        : Number(value);
      return (
        !isNaN(inputNum) && !isNaN(conditionNum) && inputNum > conditionNum
      );

    case "lessThan":
      if (
        inputValue === undefined ||
        inputValue === null ||
        value === undefined ||
        value === null
      ) {
        return false;
      }
      // Convert to numbers or dates for comparison
      const inputNumLess = isNaN(inputValue)
        ? new Date(inputValue).getTime()
        : Number(inputValue);
      const conditionNumLess = isNaN(value)
        ? new Date(value).getTime()
        : Number(value);
      return (
        !isNaN(inputNumLess) &&
        !isNaN(conditionNumLess) &&
        inputNumLess < conditionNumLess
      );

    default:
      throw new Error(`Unsupported condition type: ${evalType}`);
  }
}

/**
 * Helper function to create form data from watch function for React Hook Form compatibility
 * @param {Function} watch - React Hook Form watch function
 * @param {Array} fieldNames - Array of field names to watch
 * @returns {Object} - Object with field-value pairs
 */
export function createFormDataFromWatch(
  watch: any,
  fieldNames: string[]
): Record<string, any> {
  const formData: Record<string, any> = {};
  fieldNames.forEach((fieldName) => {
    const watchedValue = watch(fieldName);
    if (watchedValue !== undefined && watchedValue !== null) {
      if (typeof watchedValue === "object") {
        formData[fieldName] = watchedValue.value;
        formData[`${fieldName}DisplayName`] = watchedValue.label;
      } else {
        formData[fieldName] = watchedValue;
      }
    }
  });
  return formData;
}

/**
 * Helper function to create form data from getValues with watch fallback
 * @param {Function} getValues - React Hook Form getValues function
 * @param {Function} watch - React Hook Form watch function (fallback)
 * @param {Array} fieldNames - Array of field names to get
 * @returns {Object} - Object with field-value pairs
 */
export function createFormDataFromGetValues(
  watch: any,
  fieldNames: string[]
): Record<string, any> {
  const formData: Record<string, any> = {};

  fieldNames.forEach((fieldName) => {
    // Try getValues first
    let fieldValue = watch(fieldName);

    if (fieldValue !== undefined && fieldValue !== null) {
      if (typeof fieldValue === "object") {
        formData[fieldName] = fieldValue.value;
        formData[`${fieldName}DisplayName`] = fieldValue.label;
      } else {
        formData[fieldName] = fieldValue;
      }
    }
  });

  return formData;
}

/**
 * Enhanced checkVisibility function that works with React Hook Form
 * @param {Object} visibleWhenProps - Visibility condition object
 * @param {Function} getValues - React Hook Form getValues function
 * @param {Function} watch - React Hook Form watch function (fallback)
 * @returns {boolean} - Whether the field should be visible
 */
export function checkVisibility(
  visibleWhenProps: any,
  watch?: any,
  additionalData?: any
): boolean {
  if (!visibleWhenProps) {
    return true;
  }

  // Extract all field names from the condition
  const fieldNames = extractFieldNames(visibleWhenProps);

  // Create form data object with getValues as primary and watch as fallback
  const formData = createFormDataFromGetValues(watch, fieldNames);

  if (additionalData && Object.keys(formData).length === 0) {
    return evaluateConditions(visibleWhenProps, {
      ...additionalData,
    });
  }

  return evaluateConditions(visibleWhenProps, formData);
}

/**
 * Recursively extracts all field names from a condition object
 * @param {Object} condition - Condition object
 * @returns {Array} - Array of unique field names
 */
function extractFieldNames(condition: any): string[] {
  const fieldNames: string[] = [];

  function traverse(obj: any) {
    if (!obj) return;

    if (obj.fieldName) {
      fieldNames.push(obj.fieldName);
    }

    if (obj.rules && Array.isArray(obj.rules)) {
      obj.rules.forEach(traverse);
    }

    if (obj.conditions && Array.isArray(obj.conditions)) {
      obj.conditions.forEach(traverse);
    }
  }

  if (Array.isArray(condition)) {
    condition.forEach(traverse);
  } else {
    traverse(condition);
  }

  // Return unique field names
  return Array.from(new Set(fieldNames));
}
